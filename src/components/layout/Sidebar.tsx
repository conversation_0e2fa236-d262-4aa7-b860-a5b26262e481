import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Home, Mic, Volume2, Key, BarChart3, FileText, Settings, LogOut } from 'lucide-react';
import { useAuth } from '../../contexts/AuthContext';
import { clsx } from 'clsx';

const menuItems = [
  { icon: Home, label: '仪表盘', path: '/dashboard' },
  { icon: Volume2, label: '声音实验室', path: '/voice-lab' },
  { icon: Mic, label: '语音克隆', path: '/voice-clone' },
  { icon: Key, label: 'API 密钥', path: '/api-keys' },
  { icon: BarChart3, label: '用量统计', path: '/usage' },
  { icon: FileText, label: '文档', path: '/docs' },
  { icon: Settings, label: '设置', path: '/settings' },
];

export const Sidebar: React.FC = () => {
  const location = useLocation();
  const { logout } = useAuth();

  return (
    <div className="fixed left-0 top-0 h-full w-64 bg-gray-900 border-r border-gray-800">
      <div className="p-6">
        <div className="flex items-center space-x-2 mb-8">
          <img src="/logo-32x32.png" alt="SoulVoice Logo" className="h-8 w-8" />
          <span className="text-xl font-bold text-white">SoulVoice</span>
        </div>

        <nav className="space-y-2">
          {menuItems.map((item) => {
            const Icon = item.icon;
            const isActive = location.pathname === item.path;
            
            return (
              <Link
                key={item.path}
                to={item.path}
                className={clsx(
                  'flex items-center space-x-3 px-3 py-2 rounded-lg transition-all duration-200',
                  {
                    'bg-gradient-to-r from-purple-500/20 to-blue-500/20 text-white border border-purple-500/30': isActive,
                    'text-gray-400 hover:text-white hover:bg-gray-800': !isActive,
                  }
                )}
              >
                <Icon className="h-5 w-5" />
                <span className="text-sm font-medium">{item.label}</span>
              </Link>
            );
          })}
        </nav>

        <div className="absolute bottom-6 left-6 right-6">
          <button
            onClick={logout}
            className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-400 hover:text-white hover:bg-gray-800 transition-all duration-200 w-full"
          >
            <LogOut className="h-5 w-5" />
            <span className="text-sm font-medium">退出登录</span>
          </button>
        </div>
      </div>
    </div>
  );
};