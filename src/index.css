@tailwind base;
@tailwind components;
@tailwind utilities;

/* 全局深色滚动条样式 */
* {
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

/* Webkit 浏览器滚动条样式 */
*::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

*::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 4px;
}

*::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 4px;
  border: 1px solid #374151;
}

*::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

*::-webkit-scrollbar-thumb:active {
  background-color: #9ca3af;
}

*::-webkit-scrollbar-corner {
  background-color: #1f2937;
}

/* 自定义滚动条样式类 */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #4b5563 #1f2937;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background-color: #1f2937;
  border-radius: 3px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 3px;
  border: 1px solid #374151;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background-color: #6b7280;
}

.scrollbar-thin::-webkit-scrollbar-corner {
  background-color: #1f2937;
}

/* 兼容旧的类名 */
.scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
  background-color: #4b5563;
  border-radius: 0.375rem;
}

.scrollbar-track-gray-800::-webkit-scrollbar-track {
  background-color: #1f2937;
}
