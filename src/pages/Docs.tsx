import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Book, 
  Code, 
  Play, 
  Copy, 
  CheckCircle,
  ExternalLink,
  Search,
  ChevronRight,
  Zap,
  Shield,
  Globe
} from 'lucide-react';
import { Sidebar } from '../components/layout/Sidebar';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Input } from '../components/ui/Input';

export const Docs: React.FC = () => {
  const [activeSection, setActiveSection] = useState('quick-start');
  const [copied, setCopied] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');

  const sections = [
    {
      id: 'quick-start',
      title: '快速开始',
      icon: Zap,
      items: [
        { id: 'introduction', title: '产品介绍' },
        { id: 'authentication', title: '身份认证' },
        { id: 'first-request', title: '第一个请求' },
      ]
    },
    {
      id: 'api-reference',
      title: 'API 参考',
      icon: Code,
      items: [
        { id: 'text-to-speech', title: '语音合成' },
        { id: 'voice-cloning', title: '语音克隆' },
        { id: 'voice-list', title: '音色列表' },
      ]
    },
    {
      id: 'guides',
      title: '开发指南',
      icon: Book,
      items: [
        { id: 'best-practices', title: '最佳实践' },
        { id: 'error-handling', title: '错误处理' },
        { id: 'rate-limiting', title: '频率限制' },
      ]
    },
    {
      id: 'security',
      title: '安全',
      icon: Shield,
      items: [
        { id: 'api-keys', title: 'API 密钥管理' },
        { id: 'webhooks', title: 'Webhook 安全' },
      ]
    }
  ];

  const codeExamples = {
    curl: `curl -X POST https://api.soulvoice.com/v1/tts \\
  -H "Authorization: Bearer YOUR_API_KEY" \\
  -H "Content-Type: application/json" \\
  -d '{
    "text": "你好，欢迎使用 SoulVoice！",
    "voice": "zh-CN-XiaoxiaoNeural",
    "emotion": "natural",
    "speed": 1.0,
    "pitch": 1.0
  }' \\
  --output audio.mp3`,
    
    python: `import requests

response = requests.post(
    'https://api.soulvoice.com/v1/tts',
    headers={
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
    },
    json={
        'text': '你好，欢迎使用 SoulVoice！',
        'voice': 'zh-CN-XiaoxiaoNeural',
        'emotion': 'natural',
        'speed': 1.0,
        'pitch': 1.0
    }
)

if response.status_code == 200:
    with open('audio.mp3', 'wb') as f:
        f.write(response.content)
    print('语音生成成功！')
else:
    print(f'错误: {response.status_code}')`,
    
    javascript: `const response = await fetch('https://api.soulvoice.com/v1/tts', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    text: '你好，欢迎使用 SoulVoice！',
    voice: 'zh-CN-XiaoxiaoNeural',
    emotion: 'natural',
    speed: 1.0,
    pitch: 1.0
  })
});

if (response.ok) {
  const audioBlob = await response.blob();
  const audioUrl = URL.createObjectURL(audioBlob);
  
  const audio = new Audio(audioUrl);
  audio.play();
} else {
  console.error('生成失败:', response.status);
}`
  };

  const handleCopy = (code: string, id: string) => {
    navigator.clipboard.writeText(code);
    setCopied(id);
    setTimeout(() => setCopied(null), 2000);
  };

  const renderContent = () => {
    switch (activeSection) {
      case 'quick-start':
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-4">快速开始</h2>
              <p className="text-gray-400 mb-6">
                欢迎使用 SoulVoice API！本指南将帮助您在几分钟内完成第一次语音合成。
              </p>
            </div>

            <Card glass>
              <h3 className="text-xl font-semibold mb-4">1. 获取 API 密钥</h3>
              <p className="text-gray-400 mb-4">
                首先，您需要在控制台中创建一个 API 密钥：
              </p>
              <ol className="list-decimal list-inside space-y-2 text-gray-300 mb-4">
                <li>登录到您的 SoulVoice 控制台</li>
                <li>导航到 "API 密钥" 页面</li>
                <li>点击 "创建新密钥" 按钮</li>
                <li>为您的密钥命名并保存</li>
              </ol>
              <div className="bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-4">
                <p className="text-yellow-400 text-sm">
                  ⚠️ 请妥善保管您的 API 密钥，不要在客户端代码中暴露
                </p>
              </div>
            </Card>

            <Card glass>
              <h3 className="text-xl font-semibold mb-4">2. 发送第一个请求</h3>
              <p className="text-gray-400 mb-4">
                使用以下代码发送您的第一个语音合成请求：
              </p>
              
              <div className="space-y-4">
                <div className="flex space-x-2 border-b border-gray-700 pb-2">
                  {Object.keys(codeExamples).map((lang) => (
                    <button
                      key={lang}
                      className="px-3 py-1 rounded text-sm font-medium transition-colors bg-purple-500 text-white"
                    >
                      {lang.charAt(0).toUpperCase() + lang.slice(1)}
                    </button>
                  ))}
                </div>
                
                <div className="relative">
                  <pre className="bg-gray-900 p-4 rounded-lg text-sm overflow-x-auto">
                    <code className="text-green-400">
                      {codeExamples.python}
                    </code>
                  </pre>
                  <button
                    onClick={() => handleCopy(codeExamples.python, 'python')}
                    className="absolute top-2 right-2 p-2 bg-gray-800 rounded hover:bg-gray-700 transition-colors"
                  >
                    {copied === 'python' ? (
                      <CheckCircle className="h-4 w-4 text-green-400" />
                    ) : (
                      <Copy className="h-4 w-4 text-gray-400" />
                    )}
                  </button>
                </div>
              </div>
            </Card>

            <Card glass>
              <h3 className="text-xl font-semibold mb-4">3. 处理响应</h3>
              <p className="text-gray-400 mb-4">
                成功的请求将返回音频文件的二进制数据。您可以：
              </p>
              <ul className="list-disc list-inside space-y-2 text-gray-300">
                <li>将数据保存为 MP3 或 WAV 文件</li>
                <li>直接在浏览器中播放</li>
                <li>流式传输给用户</li>
              </ul>
            </Card>
          </div>
        );

      case 'api-reference':
        return (
          <div className="space-y-8">
            <div>
              <h2 className="text-2xl font-bold mb-4">API 参考</h2>
              <p className="text-gray-400 mb-6">
                完整的 SoulVoice API 端点和参数说明。
              </p>
            </div>

            <Card glass>
              <h3 className="text-xl font-semibold mb-4">语音合成 API</h3>
              <div className="space-y-4">
                <div className="bg-gray-800 p-3 rounded-lg">
                  <code className="text-green-400">POST /v1/tts</code>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">请求参数</h4>
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-gray-700">
                          <th className="text-left py-2">参数</th>
                          <th className="text-left py-2">类型</th>
                          <th className="text-left py-2">必需</th>
                          <th className="text-left py-2">说明</th>
                        </tr>
                      </thead>
                      <tbody className="text-gray-300">
                        <tr className="border-b border-gray-800">
                          <td className="py-2"><code>text</code></td>
                          <td>string</td>
                          <td>是</td>
                          <td>要合成的文本内容</td>
                        </tr>
                        <tr className="border-b border-gray-800">
                          <td className="py-2"><code>voice</code></td>
                          <td>string</td>
                          <td>是</td>
                          <td>音色 ID</td>
                        </tr>
                        <tr className="border-b border-gray-800">
                          <td className="py-2"><code>emotion</code></td>
                          <td>string</td>
                          <td>否</td>
                          <td>情感风格（natural, happy, sad 等）</td>
                        </tr>
                        <tr className="border-b border-gray-800">
                          <td className="py-2"><code>speed</code></td>
                          <td>number</td>
                          <td>否</td>
                          <td>语速倍率（0.5-2.0，默认 1.0）</td>
                        </tr>
                        <tr>
                          <td className="py-2"><code>pitch</code></td>
                          <td>number</td>
                          <td>否</td>
                          <td>音调倍率（0.5-2.0，默认 1.0）</td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold mb-2">响应</h4>
                  <p className="text-gray-400 mb-2">
                    成功时返回音频文件的二进制数据（Content-Type: audio/mpeg）
                  </p>
                  <div className="bg-gray-900 p-3 rounded-lg">
                    <code className="text-blue-400">
                      HTTP/1.1 200 OK<br/>
                      Content-Type: audio/mpeg<br/>
                      Content-Length: 12345<br/><br/>
                      [音频二进制数据]
                    </code>
                  </div>
                </div>
              </div>
            </Card>

            <Card glass>
              <h3 className="text-xl font-semibold mb-4">错误响应</h3>
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="w-full text-sm">
                    <thead>
                      <tr className="border-b border-gray-700">
                        <th className="text-left py-2">状态码</th>
                        <th className="text-left py-2">说明</th>
                      </tr>
                    </thead>
                    <tbody className="text-gray-300">
                      <tr className="border-b border-gray-800">
                        <td className="py-2"><code>400</code></td>
                        <td>请求参数错误</td>
                      </tr>
                      <tr className="border-b border-gray-800">
                        <td className="py-2"><code>401</code></td>
                        <td>API 密钥无效或缺失</td>
                      </tr>
                      <tr className="border-b border-gray-800">
                        <td className="py-2"><code>429</code></td>
                        <td>请求频率超限</td>
                      </tr>
                      <tr>
                        <td className="py-2"><code>500</code></td>
                        <td>服务器内部错误</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </Card>
          </div>
        );

      default:
        return (
          <div className="text-center py-12">
            <Book className="h-12 w-12 text-gray-500 mx-auto mb-4" />
            <p className="text-gray-400">选择左侧菜单查看文档内容</p>
          </div>
        );
    }
  };

  return (
    <div className="min-h-screen bg-gray-900 text-white">
      <Sidebar />
      
      <div className="ml-64 flex">
        {/* Documentation Sidebar */}
        <div className="w-80 border-r border-gray-800 p-6 h-screen overflow-y-auto">
          <div className="space-y-6">
            <div>
              <h2 className="text-xl font-bold mb-4">文档</h2>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="搜索文档..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <nav className="space-y-2">
              {sections.map((section) => (
                <div key={section.id}>
                  <button
                    onClick={() => setActiveSection(section.id)}
                    className={`w-full flex items-center space-x-2 px-3 py-2 rounded-lg text-left transition-colors ${
                      activeSection === section.id
                        ? 'bg-purple-500/20 text-purple-400'
                        : 'text-gray-400 hover:text-white hover:bg-gray-800'
                    }`}
                  >
                    <section.icon className="h-4 w-4" />
                    <span className="font-medium">{section.title}</span>
                  </button>
                  
                  {activeSection === section.id && (
                    <div className="ml-6 mt-2 space-y-1">
                      {section.items.map((item) => (
                        <button
                          key={item.id}
                          className="w-full text-left px-3 py-1 text-sm text-gray-400 hover:text-white transition-colors"
                        >
                          {item.title}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </nav>

            <div className="pt-6 border-t border-gray-800">
              <h3 className="font-semibold mb-3">快速链接</h3>
              <div className="space-y-2">
                <a href="#" className="flex items-center space-x-2 text-sm text-gray-400 hover:text-white">
                  <ExternalLink className="h-4 w-4" />
                  <span>API 状态页面</span>
                </a>
                <a href="#" className="flex items-center space-x-2 text-sm text-gray-400 hover:text-white">
                  <Globe className="h-4 w-4" />
                  <span>社区论坛</span>
                </a>
                <a href="#" className="flex items-center space-x-2 text-sm text-gray-400 hover:text-white">
                  <Book className="h-4 w-4" />
                  <span>更新日志</span>
                </a>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 p-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            {renderContent()}
          </motion.div>
        </div>
      </div>
    </div>
  );
};