import React, { useState, useEffect } from 'react';
import { VoiceService, type Voice } from '../services/voiceService';
import { TTSService } from '../services/ttsService';
import { useAuth } from '../contexts/AuthContext';
import { Card } from '../components/ui/Card';
import { Button } from '../components/ui/Button';
import { Volume2, Play, RefreshCw, Plus, Trash2 } from 'lucide-react';

export const VoiceTest: React.FC = () => {
  const { user } = useAuth();
  const [voices, setVoices] = useState<Voice[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedVoice, setSelectedVoice] = useState<string>('');
  const [testText, setTestText] = useState('你好，这是音色测试');
  const [generating, setGenerating] = useState(false);

  useEffect(() => {
    loadVoices();
  }, [user]);

  const loadVoices = async () => {
    try {
      setLoading(true);
      setError(null);
      const voiceList = await VoiceService.getAllVoices(user?.id);
      setVoices(voiceList);
      if (voiceList.length > 0 && !selectedVoice) {
        setSelectedVoice(voiceList[0].id);
      }
    } catch (err) {
      console.error('Error loading voices:', err);
      setError('加载音色列表失败');
    } finally {
      setLoading(false);
    }
  };

  const testVoice = async (voiceId: string) => {
    if (!user) {
      setError('请先登录');
      return;
    }

    try {
      setGenerating(true);
      setError(null);

      const response = await TTSService.generateSpeech({
        model: 'fnlp/MOSS-TTSD-v0.5',
        input: testText,
        voice: voiceId,
      }, user.id);

      if (response.success && response.audioUrl) {
        const audio = new Audio(response.audioUrl);
        audio.play();
      } else {
        setError(response.error || '生成语音失败');
      }
    } catch (err) {
      console.error('Error testing voice:', err);
      setError('测试音色失败');
    } finally {
      setGenerating(false);
    }
  };

  const syncFromAPI = async () => {
    if (!user) {
      setError('请先登录');
      return;
    }

    try {
      setLoading(true);
      setError(null);
      await VoiceService.syncVoicesFromAPI(user.id);
      await loadVoices();
    } catch (err) {
      console.error('Error syncing voices:', err);
      setError('同步音色失败');
    } finally {
      setLoading(false);
    }
  };

  const deleteVoice = async (voiceId: string) => {
    if (!user) return;

    try {
      await VoiceService.deleteVoice(voiceId, user.id);
      await loadVoices();
    } catch (err) {
      console.error('Error deleting voice:', err);
      setError('删除音色失败');
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 p-6">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-2">音色管理测试</h1>
          <p className="text-gray-300">测试新的音色管理系统</p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-500/20 border border-red-500/50 rounded-lg text-red-400">
            {error}
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 音色列表 */}
          <Card glass>
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-semibold text-white">音色列表</h2>
              <div className="flex space-x-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={syncFromAPI}
                  disabled={loading || !user}
                >
                  <Plus className="h-4 w-4 mr-1" />
                  同步API
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={loadVoices}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                </Button>
              </div>
            </div>

            {loading ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
              </div>
            ) : voices.length === 0 ? (
              <div className="text-center py-8">
                <Volume2 className="h-12 w-12 text-gray-600 mx-auto mb-3" />
                <p className="text-gray-400">暂无音色</p>
              </div>
            ) : (
              <div className="max-h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
                <div className="space-y-2 pr-2">
                  {voices.map((voice) => (
                    <div
                      key={voice.id}
                      className={`p-3 rounded-lg border transition-all duration-200 ${
                        selectedVoice === voice.id
                          ? 'bg-purple-500/20 border-purple-500/50'
                          : 'bg-gray-800 border-gray-700 hover:bg-gray-700'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-white truncate">{voice.name}</span>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              voice.voice_type === 'system' 
                                ? 'bg-blue-500/20 text-blue-400'
                                : voice.voice_type === 'cloned'
                                ? 'bg-green-500/20 text-green-400'
                                : 'bg-yellow-500/20 text-yellow-400'
                            }`}>
                              {voice.voice_type === 'system' ? '系统' : 
                               voice.voice_type === 'cloned' ? '克隆' : '自定义'}
                            </span>
                            {voice.gender && (
                              <span className={`px-2 py-1 text-xs rounded-full ${
                                voice.gender === 'female' 
                                  ? 'bg-pink-500/20 text-pink-400' 
                                  : 'bg-blue-500/20 text-blue-400'
                              }`}>
                                {voice.gender === 'female' ? '女声' : '男声'}
                              </span>
                            )}
                          </div>
                          <p className="text-sm text-gray-400 mt-1 truncate">
                            {voice.preview_text || voice.description}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2 ml-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setSelectedVoice(voice.id)}
                            className="p-2"
                          >
                            <Volume2 className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => testVoice(voice.id)}
                            disabled={generating}
                            className="p-2"
                          >
                            <Play className="h-4 w-4" />
                          </Button>
                          {voice.voice_type !== 'system' && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => deleteVoice(voice.id)}
                              className="p-2 text-red-400 hover:text-red-300"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </Card>

          {/* 测试面板 */}
          <Card glass>
            <h2 className="text-xl font-semibold text-white mb-4">音色测试</h2>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  测试文本
                </label>
                <textarea
                  value={testText}
                  onChange={(e) => setTestText(e.target.value)}
                  className="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500"
                  rows={3}
                  placeholder="输入要测试的文本..."
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  选中的音色
                </label>
                <div className="p-3 bg-gray-800 border border-gray-700 rounded-lg">
                  {selectedVoice ? (
                    <div>
                      <p className="text-white font-medium">
                        {voices.find(v => v.id === selectedVoice)?.name || '未知音色'}
                      </p>
                      <p className="text-sm text-gray-400">
                        ID: {selectedVoice}
                      </p>
                    </div>
                  ) : (
                    <p className="text-gray-400">请选择一个音色</p>
                  )}
                </div>
              </div>

              <Button
                onClick={() => selectedVoice && testVoice(selectedVoice)}
                disabled={!selectedVoice || generating || !testText.trim()}
                className="w-full"
              >
                {generating ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    生成中...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4 mr-2" />
                    测试音色
                  </>
                )}
              </Button>
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
};
