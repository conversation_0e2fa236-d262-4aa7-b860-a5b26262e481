import { supabase } from '../lib/supabase';

export interface Voice {
  id: string;
  name: string;
  description?: string;
  uri: string;
  model: string;
  voice_type: 'system' | 'user_custom' | 'cloned';
  user_id?: string;
  gender?: 'male' | 'female' | 'neutral';
  language: string;
  preview_text?: string;
  is_active: boolean;
  sort_order: number;
  metadata?: any;
  created_at: string;
  updated_at: string;
}

export interface CreateVoiceRequest {
  name: string;
  description?: string;
  uri: string;
  model?: string;
  voice_type: 'user_custom' | 'cloned';
  gender?: 'male' | 'female' | 'neutral';
  language?: string;
  preview_text?: string;
  metadata?: any;
}

export class VoiceService {
  // 缓存机制
  private static voiceCache: Map<string, Voice[]> = new Map();
  private static cacheTimestamp: Map<string, number> = new Map();
  private static readonly CACHE_DURATION = 3 * 60 * 1000; // 3分钟缓存

  /**
   * 获取所有可用音色（系统音色 + 用户音色）
   */
  static async getAllVoices(userId?: string): Promise<Voice[]> {
    const cacheKey = userId || 'system';

    // 检查缓存
    if (this.isCacheValid(cacheKey)) {
      const cachedVoices = this.voiceCache.get(cacheKey);
      if (cachedVoices) {
        console.log(`[VoiceService] Using cached voices for ${cacheKey}`, { count: cachedVoices.length });
        return cachedVoices;
      }
    }

    try {
      console.log(`[VoiceService] Fetching voices from database for ${cacheKey}`);

      let query = supabase
        .from('voices')
        .select('*')
        .eq('is_active', true);

      if (userId) {
        // 获取系统音色和用户自己的音色
        query = query.or(`voice_type.eq.system,user_id.eq.${userId}`);
      } else {
        // 只获取系统音色
        query = query.eq('voice_type', 'system');
      }

      console.log(`[VoiceService] Executing query for ${cacheKey}`);
      const { data, error } = await query.order('sort_order').order('created_at');

      if (error) {
        console.error(`[VoiceService] Database error for ${cacheKey}:`, error);
        throw error;
      }

      const voices = data || [];

      // 更新缓存
      this.voiceCache.set(cacheKey, voices);
      this.cacheTimestamp.set(cacheKey, Date.now());

      console.log(`[VoiceService] Voices fetched successfully for ${cacheKey}`, {
        count: voices.length,
        voices: voices.map(v => ({ id: v.id, name: v.name, voice_type: v.voice_type }))
      });
      return voices;
    } catch (error) {
      console.error(`[VoiceService] Error fetching voices for ${cacheKey}:`, error);

      // 如果是数据库连接问题，返回空数组而不是抛出错误
      if (error && typeof error === 'object' && 'code' in error) {
        console.warn(`[VoiceService] Database connection issue, returning empty array`);
        return [];
      }

      throw error;
    }
  }

  /**
   * 检查缓存是否有效
   */
  private static isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamp.get(cacheKey);
    return timestamp ? (Date.now() - timestamp < this.CACHE_DURATION) : false;
  }

  /**
   * 清除缓存
   */
  static clearCache(userId?: string): void {
    if (userId) {
      const cacheKey = userId;
      this.voiceCache.delete(cacheKey);
      this.cacheTimestamp.delete(cacheKey);
      console.log(`[VoiceService] Cleared cache for user ${userId}`);
    } else {
      this.voiceCache.clear();
      this.cacheTimestamp.clear();
      console.log('[VoiceService] Cleared all cache');
    }
  }

  /**
   * 获取系统音色
   */
  static async getSystemVoices(): Promise<Voice[]> {
    try {
      const { data, error } = await supabase
        .from('voices')
        .select('*')
        .eq('voice_type', 'system')
        .eq('is_active', true)
        .order('sort_order');

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching system voices:', error);
      throw error;
    }
  }

  /**
   * 获取用户自定义音色
   */
  static async getUserVoices(userId: string): Promise<Voice[]> {
    try {
      const { data, error } = await supabase
        .from('voices')
        .select('*')
        .eq('user_id', userId)
        .eq('is_active', true)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    } catch (error) {
      console.error('Error fetching user voices:', error);
      throw error;
    }
  }

  /**
   * 根据 URI 查找音色
   */
  static async findVoiceByUri(uri: string, userId?: string): Promise<Voice | null> {
    try {
      let query = supabase
        .from('voices')
        .select('*')
        .eq('uri', uri)
        .eq('is_active', true);

      if (userId) {
        query = query.or(`voice_type.eq.system,user_id.eq.${userId}`);
      } else {
        query = query.eq('voice_type', 'system');
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || null;
    } catch (error) {
      console.error('Error finding voice by URI:', error);
      return null;
    }
  }

  /**
   * 根据 ID 查找音色
   */
  static async findVoiceById(id: string, userId?: string): Promise<Voice | null> {
    try {
      let query = supabase
        .from('voices')
        .select('*')
        .eq('id', id)
        .eq('is_active', true);

      if (userId) {
        query = query.or(`voice_type.eq.system,user_id.eq.${userId}`);
      } else {
        query = query.eq('voice_type', 'system');
      }

      const { data, error } = await query.single();

      if (error && error.code !== 'PGRST116') throw error;
      return data || null;
    } catch (error) {
      console.error('Error finding voice by ID:', error);
      return null;
    }
  }

  /**
   * 创建用户自定义音色
   */
  static async createVoice(userId: string, voiceData: CreateVoiceRequest): Promise<Voice> {
    try {
      const { data, error } = await supabase
        .from('voices')
        .insert({
          ...voiceData,
          user_id: userId,
          model: voiceData.model || 'fnlp/MOSS-TTSD-v0.5',
          language: voiceData.language || 'zh-CN',
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error creating voice:', error);
      throw error;
    }
  }

  /**
   * 更新音色信息
   */
  static async updateVoice(
    voiceId: string,
    userId: string,
    updates: Partial<CreateVoiceRequest>
  ): Promise<Voice> {
    try {
      const { data, error } = await supabase
        .from('voices')
        .update(updates)
        .eq('id', voiceId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error updating voice:', error);
      throw error;
    }
  }

  /**
   * 删除音色（软删除）
   */
  static async deleteVoice(voiceId: string, userId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('voices')
        .update({ is_active: false })
        .eq('id', voiceId)
        .eq('user_id', userId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting voice:', error);
      throw error;
    }
  }

  /**
   * 从 SiliconFlow API 同步音色
   */
  static async syncVoicesFromAPI(userId: string): Promise<Voice[]> {
    try {
      // 调用 SiliconFlow API 获取音色列表
      const response = await fetch('https://api.siliconflow.cn/v1/audio/speech/voices', {
        headers: {
          'Authorization': 'Bearer sk-ynabzdlcumjklweexfyruujoydkzfnqctcnlsiifbloqgdcw',
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const apiVoices = data.result || [];

      // 为每个 API 音色创建数据库记录
      const createdVoices: Voice[] = [];
      for (const apiVoice of apiVoices) {
        try {
          // 检查是否已存在
          const existing = await this.findVoiceByUri(apiVoice.uri, userId);
          if (existing) continue;

          const voice = await this.createVoice(userId, {
            name: apiVoice.customName || `自定义音色 ${apiVoice.uri.split(':')[1]}`,
            description: `从 SiliconFlow 同步的音色`,
            uri: apiVoice.uri,
            voice_type: 'user_custom',
            preview_text: apiVoice.text || '这是一个自定义音色',
            metadata: {
              source: 'siliconflow_api',
              originalData: apiVoice,
            },
          });
          createdVoices.push(voice);
        } catch (error) {
          console.error('Error creating voice from API:', error);
        }
      }

      return createdVoices;
    } catch (error) {
      console.error('Error syncing voices from API:', error);
      throw error;
    }
  }

  /**
   * 格式化音色数据为前端使用的格式
   */
  static formatVoiceForUI(voice: Voice): {
    id: string;
    name: string;
    preview: string;
    isCustom: boolean;
    isCloned: boolean;
    uri: string;
    gender?: string;
  } {
    return {
      id: voice.id,
      name: voice.name,
      preview: voice.preview_text || voice.description || '暂无预览',
      isCustom: voice.voice_type !== 'system',
      isCloned: voice.voice_type === 'cloned',
      uri: voice.uri,
      gender: voice.gender,
    };
  }

  /**
   * 批量格式化音色数据
   */
  static formatVoicesForUI(voices: Voice[]): Array<{
    id: string;
    name: string;
    preview: string;
    isCustom: boolean;
    isCloned: boolean;
    uri: string;
    gender?: string;
  }> {
    return voices.map(voice => this.formatVoiceForUI(voice));
  }
}
